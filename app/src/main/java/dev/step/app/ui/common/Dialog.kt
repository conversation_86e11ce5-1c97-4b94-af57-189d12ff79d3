package dev.step.app.ui.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.zIndex
import com.roudikk.guia.core.toDialogProperties
import com.roudikk.guia.extensions.localDialog
import dev.step.app.ui.theme.*
import dev.step.app.R
import dev.step.app.androidplatform.biz.ad.NativeAdInDialog
import dev.step.app.androidplatform.biz.ad.NativeAdPlace

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AppDefDialog(
    onDismiss: () -> Unit,
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
    cancelText: String = stringResource(id = R.string.text_cancel),
    confirmText: String = stringResource(id = R.string.text_save),
    properties: DialogProperties = localDialog()?.dialogOptions?.toDialogProperties()
        ?: DialogProperties(),
    content: @Composable () -> Unit
) {
    Dialog(onDismissRequest = onDismiss, properties = properties) {

        Surface(
            shape = RoundedCornerShape7Dp,
            modifier = if (properties.usePlatformDefaultWidth) Modifier else Modifier
                .fillMaxWidth()
                .padding(horizontal = 22.dp)
        ) {
            Column(modifier = Modifier.bodyWidth()) {
                BlankSpacer(height = 24.dp)

                content()

                Row(modifier = Modifier.bodyWidth()) {
                    Surface(
                        onClick = onCancel,
                        elevation = 0.dp,
                        modifier = Modifier.size(104.dp, 36.dp),
                        shape = CircleShape
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(AppColor.PrimaryLight.copy(alpha = .12f))
                        ) {
                            Text(
                                text = cancelText,
                                color = AppColor.TextColorBlack,
                                fontSize = 15.sp,
                                modifier = Modifier.align(
                                    Alignment.Center
                                )
                            )
                        }
                    }

                    BlankSpacer(width = 36.dp)

                    Surface(
                        onClick = onConfirm,
                        elevation = 0.dp,
                        modifier = Modifier.size(104.dp, 36.dp),
                        shape = CircleShape
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(AppColor.FadedPrimaryBrushVertical)
                        ) {
                            Text(
                                text = confirmText,
                                color = Color.White,
                                fontSize = 15.sp,
                                modifier = Modifier.align(
                                    Alignment.Center
                                )
                            )
                        }
                    }
                }

                BlankSpacer(height = 24.dp)
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AppDefWithCloseDialog(
    onDismiss: () -> Unit,
    onClose: () -> Unit,
    onConfirm: () -> Unit,
    confirmText: String?,
    topPainter: Painter?,
    adPlace: NativeAdPlace?,
    adPlaceName: String?,
    showCloseIcon: Boolean = true,
    bottomPadding: Dp = 20.dp,
    properties: DialogProperties = localDialog()?.dialogOptions?.toDialogProperties()
        ?: DialogProperties(),
    content: @Composable () -> Unit,
) {
    Dialog(onDismissRequest = onDismiss, properties = properties) {
        Column(
            modifier = if (properties.usePlatformDefaultWidth) Modifier else Modifier
                .fillMaxWidth()
                .padding(horizontal = 22.dp)
        ) {
            Box {
                topPainter?.let {
                    Image(
                        painter = topPainter,
                        contentDescription = null,
                        modifier = Modifier
                            .size(78.dp)
                            .align(Alignment.TopCenter)
                            .zIndex(1f)
                    )
                }

                val dialogCardPaddingTop = if (topPainter == null) 0.dp else 36.dp

                Surface(
                    shape = RoundedCornerShape7Dp,
                    modifier = Modifier.padding(top = dialogCardPaddingTop)
                ) {
                    Column(modifier = Modifier.fillMaxWidth()) {

                        BlankSpacer(height = bottomPadding)

                        content()

                        confirmText?.let {
                            Surface(
                                onClick = onConfirm,
                                elevation = 0.dp,
                                shape = CircleShape,
                                modifier = Modifier.bodyWidth()
                            ) {
                                Box(modifier = Modifier.background(brush = AppColor.FadedPrimaryBrushVertical)) {
                                    Text(
                                        text = confirmText,
                                        color = Color.White,
                                        fontSize = 17.sp,
                                        modifier = Modifier.padding(
                                            vertical = 8.dp,
                                            horizontal = 50.dp
                                        )
                                    )
                                }
                            }
                        }

                        BlankSpacer(height = bottomPadding)
                    }
                }
            }

            adPlace?.let {
                BlankSpacer(height = 12.dp)

                Surface(
                    shape = RoundedCornerShape(10.dp),
                ) {
//                    MRecAd(
//                        placeholder = adPlaceholder,
//                        placeName = adPlaceName!!,
//                        modifier = Modifier.padding(top = 8.dp)
//                    )
                    NativeAdInDialog(place = adPlace, modifier = Modifier.padding(bottom = 4.dp))
                }
            }

            if (showCloseIcon) {
                Surface(
                    onClick = onClose,
                    modifier = Modifier.bodyWidth(),
                    shape = CircleShape,
                    color = Color.LightGray.copy(.8f)
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Close,
                        contentDescription = null,
                        modifier = Modifier.size(22.dp),
                        tint = Color.DarkGray
                    )
                }

            }
        }
    }
}

//@Preview
//@Composable
//private fun AppDefDialogPreview() {
//    AppDefDialog(
//        {},
//        {},
//        {},
//    ) {
//
//    }
//}

@Preview
@Composable
private fun AppDefWithCloseDialogPreview() {
    AppDefWithCloseDialog(
        {},
        {},
        {},
        "null",
        adPlace = /*MRecAdPlaceholder.Dialog*/ null,
        adPlaceName = "lalala",
        topPainter = painterResource(id = R.drawable.img_bigmoji_sad),
        content = {
            Column {
                BlankSpacer(height = 32.dp)

                Text(text = "lalala", modifier = Modifier.bodyWidth())
                Text(
                    text = "lalalalalalalalalalalalalalalalalalalalalalalala",
                    modifier = Modifier.bodyWidth()
                )
                BlankSpacer(height = 20.dp)
            }

        },
        showCloseIcon = true,
    )
}
