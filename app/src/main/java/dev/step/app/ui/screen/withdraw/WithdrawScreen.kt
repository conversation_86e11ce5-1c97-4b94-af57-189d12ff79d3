package dev.step.app.ui.screen.withdraw

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.biz.ad.NativeAd
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.scale
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.data.pojo.remoteconfig.CoinsExchangeUnit
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppColor
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.math.RoundingMode

@Composable
fun WithdrawScreen(
    navUp: () -> Unit,
    openRedeemedCoupon: (coupon: RedeemedPrize.Coupon) -> Unit,
    openRedeemedCash: (cash: RedeemedPrize.Cash) -> Unit,
    openNoEnoughCoinsDialog: () -> Unit,
) {
    WithdrawScreen(
        navUp = navUp,
        openRedeemedCoupon = openRedeemedCoupon,
        openRedeemedCash = openRedeemedCash,
        openNoEnoughCoinsDialog = openNoEnoughCoinsDialog,
        viewModel = koinViewModel()
    )
}

@Composable
private fun WithdrawScreen(
    navUp: () -> Unit,
    openRedeemedCoupon: (coupon: RedeemedPrize.Coupon) -> Unit,
    openRedeemedCash: (cash: RedeemedPrize.Cash) -> Unit,
    openNoEnoughCoinsDialog: () -> Unit,
    viewModel: WithdrawViewModel,
) {
    val context = LocalContext.current

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh(context)
            else -> {}
        }
    }

    val viewState by viewModel.collectAsState()

    viewModel.collectSideEffect {
        handleWithdrawSideEffect(
            it = it,
            onToRedeemedCoupon = openRedeemedCoupon,
            onToRedeemedCash = openRedeemedCash,
            onInsufficientInCoins = openNoEnoughCoinsDialog,
        )
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            WithdrawTopBar(
                isOrganic = viewState.isOrganic ?: true,
                coinBalance = viewState.coinBalance,
                coinsExchangeUnit = viewState.coinsExchangeUnit,
                navUp = navUp
            )
        }
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            RedeemedPrizeContent(
                currency = viewState.coinsExchangeUnit?.currency_text ?: "",
                currencyPlacementIsStart = viewState.coinsExchangeUnit?.currency_text_placement_is_start == true,
                isOrganic = viewState.isOrganic,
                redeemedPrizes = viewState.redeemedPrizes,
                onExchangeRedeemedPrize = viewModel::onExchangeRedeemedPrize
            )

            BlankSpacer(height = 16.dp)

            if (viewState.isOrganic != null) {
                val placeName = when (viewState.isOrganic) {
                    true -> "withdraw_organic"
                    false -> "withdraw_paid"
                    else -> ""
                }

//                MRecAd(
//                    placeholder = MRecAdPlaceholder.WithdrawScreen,
//                    placeName = placeName
//                )

                NativeAd(
                    place = NativeAdPlace.Withdraw,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun WithdrawTopBar(
    isOrganic: Boolean,
    coinBalance: Int?,
    coinsExchangeUnit: CoinsExchangeUnit?,
    navUp: () -> Unit,
) {
    val statusBarHeight = LocalContext.current.statusBarHeight

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        BlankSpacer(height = statusBarHeight)

        Row(
            modifier = Modifier
                .padding(vertical = 8.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            IconButton(navUp) {
                Icon(
                    imageVector = Icons.Rounded.KeyboardArrowLeft,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = AppColor.TextColorBlack
                )
            }

            Text(
                text = stringResource(R.string.text_withdraw),
                fontSize = 20.sp,
            )
        }

        Surface(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(bottom = 18.dp),
            shape = RoundedCornerShape(10.dp),
            color = AppColor.PrimaryLightAlpha8
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp, vertical = if (isOrganic) 23.dp else 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_coin),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )

                BlankSpacer(width = 4.dp)

                Text(text = coinBalance.toString(), color = AppColor.Primary, fontSize = 18.sp)

                BlankSpacer(width = 4.dp)

                Spacer(modifier = Modifier.weight(1f))

                if (!isOrganic) {
                    Chip(
                        onClick = {},
                        colors = ChipDefaults.chipColors(
                            backgroundColor = AppColor.Primary,
                            contentColor = Color.White,
                            leadingIconContentColor = Color.White
                        )
                    ) {
                        val coinsPerOneUnitF =
                            coinsExchangeUnit?.one_unit_exchange_coins?.toFloat() ?: 0f

                        val unitSize = coinsExchangeUnit?.currency_unit_size ?: 1
                        val scale = when (unitSize.toString().length) {
                            1 -> 2
                            2 -> 1
                            else -> 0
                        }

                        val cash: String = if (coinsPerOneUnitF == 0f) {
                            ""
                        } else {
                            ((coinBalance ?: 0) / coinsPerOneUnitF * unitSize)
                                .scale(scale, RoundingMode.DOWN)
                                .let { f -> if (scale == 0) f.toInt() else f }
                                .toString()
                        }

                        val cashAndCurrencyText =
                            if (coinsExchangeUnit?.currency_text_placement_is_start == true) {
                                "≈${coinsExchangeUnit.currency_text}$cash"
                            } else {
                                "≈$cash${coinsExchangeUnit?.currency_text}"
                            }

                        Text(
                            text = cashAndCurrencyText,
                            color = Color.White,
                            fontSize = 17.sp
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun RedeemedPrizeContent(
    currency: String,
    currencyPlacementIsStart: Boolean,
    isOrganic: Boolean?,
    redeemedPrizes: List<RedeemedPrize>,
    onExchangeRedeemedPrize: (prize: RedeemedPrize, rewardAdFrom: String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = when (isOrganic) {
                true -> stringResource(R.string.text_choose_a_coupon)
                false -> stringResource(R.string.text_choose_an_amount)
                null -> ""
            },
            modifier = Modifier.padding(vertical = 8.dp, horizontal = 16.dp)
        )

        FlowRow(
            maxItemsInEachRow = 2,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
        ) {
            redeemedPrizes.forEachIndexed { index, redeemed ->
                when (redeemed) {
                    is RedeemedPrize.Cash -> {
                        RedeemedCashItem(
                            sid = index + 1,
                            currency = currency,
                            currencyPlacementIsStart = currencyPlacementIsStart,
                            cash = redeemed,
                            onRedeemed = {
                                val event = "withdraw_cash${index + 1}"
                                logEventRecord("click_$event")

                                onExchangeRedeemedPrize(redeemed, event)
                            },
                            modifier = Modifier
                                .padding(6.dp)
                                .weight(1f),
                        )
                    }

                    is RedeemedPrize.Coupon -> {
                        RedeemedCouponItem(
                            currency = currency,
                            currencyPlacementIsStart = currencyPlacementIsStart,
                            coupon = redeemed,
                            onRedeemed = {
                                val event = "withdraw_coupon${index + 1}"
                                logEventRecord("click_$event")

                                onExchangeRedeemedPrize(redeemed, event)
                            },
                            modifier = Modifier
                                .padding(6.dp)
                                .weight(1f),
                        )
                    }
                }
            }
        }
    }
}

//@Composable
//private fun RedeemedPrizeItem(
//    currency: String,
//    currencyPlacementIsStart: Boolean,
//    redeemedPrize: RedeemedPrize,
//    onExchangeRedeemedPrize: (RedeemedPrize) -> Unit,
//    modifier: Modifier = Modifier
//) {
//
//    val redeemedPainter = when (redeemedPrize) {
//        is RedeemedPrize.Cash -> {
//            painterResource(id = R.drawable.ic_cash)
//        }
//
//        is RedeemedPrize.GiftCard -> {
//            painterResource(id = R.drawable.ic_gift_card)
//        }
//    }
//
//    Box(modifier) {
//        Surface(
//            shape = RoundedCornerShape(8.dp),
//        ) {
//            Row(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(14.dp),
//                verticalAlignment = Alignment.CenterVertically
//            ) {
//                Image(
//                    painter = redeemedPainter,
//                    contentDescription = null,
//                    modifier = Modifier.size(32.dp)
//                )
//
//                BlankSpacer(width = 8.dp)
//
//                Column {
//                    Text(text = redeemedPrize.coinPrice.toString(), fontSize = 16.sp)
//
//                    val amountText = if (currencyPlacementIsStart) {
//                        "$currency${redeemedPrize.amount}"
//                    } else {
//                        "${redeemedPrize.amount}$currency"
//                    }
//                    Text(text = amountText, fontSize = 13.sp)
//                }
//
//                Spacer(modifier = modifier.weight(1f))
//
//                Chip(
//                    onClick = { onExchangeRedeemedPrize(redeemedPrize) },
//                    leadingIcon = {
//                        Icon(
//                            painter = painterResource(id = R.drawable.ic_rewarded_video_with_btn),
//                            contentDescription = null,
//                            modifier = Modifier
//                                .height(16.dp)
//                                .padding(start = 5.dp)
//                        )
//                    },
//                    colors = ChipDefaults.chipColors(
//                        backgroundColor = OrangeAccent,
//                        contentColor = Color.White,
//                        leadingIconContentColor = Color.White
//                    )
//                ) {
//                    Text(text = stringResource(R.string.text_get), color = Color.White)
//                }
//            }
//        }
//    }
//}

@Preview
@Composable
fun WithdrawTopBarPreview() {
    WithdrawTopBar(
        isOrganic = false,
        coinBalance = 150000,
        coinsExchangeUnit = CoinsExchangeUnit(100, 100, "HK$", true),
        navUp = {})
}