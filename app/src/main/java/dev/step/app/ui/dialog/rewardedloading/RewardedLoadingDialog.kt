package dev.step.app.ui.dialog.rewardedloading

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import dev.step.app.R
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.ad.NativeAdInDialog
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedLoadingDialogEvent
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogEventFlow
import dev.step.app.androidplatform.send
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.FancyFadedBgButton
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

val rewardedLoadingDialogTimeoutEventFlow = EventFlow<Unit>()

@Composable
fun RewardedLoadingDialog(
    instantlyLoad: Boolean,
    navUp: () -> Unit,
    popBackStack: () -> Unit,
    replaceToRewardedErrorDialog: () -> Unit,
) {
//    val maxRewardedAdHelper: MaxRewardedAdHelper = koinInject()
    val rewardedAdManager: RewardedAdManager = koinInject()

    val scope = rememberCoroutineScope()

    var hasCallTimeout by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(Unit) {
        rewardedLoadingDialogEventFlow.onEach {
            when (it) {
                is RewardedLoadingDialogEvent.StartShow,
                RewardedLoadingDialogEvent.Showing -> {
                }

                RewardedLoadingDialogEvent.LoadingTimeout -> {
                    // todo
                    if (instantlyLoad) {
                        if (!hasCallTimeout) {
                            rewardedLoadingDialogTimeoutEventFlow.send(Unit)
                            hasCallTimeout = true
                            replaceToRewardedErrorDialog()
                        }
                    }
                }

                RewardedLoadingDialogEvent.ShowAdAndDismissDialog -> {
                    if (instantlyLoad) {
                        navUp()
                        rewardedAdManager.showRewardedAd()
                    }
                }
            }
        }.launchIn(this)


        if (instantlyLoad) {
            delay(2_0000)
            if (!hasCallTimeout) {
                hasCallTimeout = true
                replaceToRewardedErrorDialog()
            }
        }
    }



    Dialog(onDismissRequest = {}) {
        Box {
            if (!instantlyLoad) {
                IconButton(
                    onClick = {
                        rewardedLoadingDialogTimeoutEventFlow.send(Unit)
                        navUp()
                    },
                    modifier = Modifier.align(Alignment.TopEnd)
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Close,
                        contentDescription = null,
                        tint = Color.White
                    )
                }
            }

            TreasureChest()

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(top = 248.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.rewarded_ad_tips_content_1),
                    color = Color.White,
                    fontSize = if (Locale.current.toLanguageTag() == "ru-RU") 17.sp else 19.sp,
                )

                BlankSpacer(height = 16.dp)

                if (instantlyLoad) {
                    Surface(
                        shape = CircleShape,
                        modifier = Modifier
                            .width(300.dp)
                            .height(14.dp),
                        border = BorderStroke(4.dp, Color.White),
                    ) {
                        LinearProgressIndicator(
                            modifier = Modifier.fillMaxSize(),
                            color = AppColor.Primary,
                            backgroundColor = Color.White,
                            strokeCap = StrokeCap.Round
                        )
                    }
                } else {
                    FancyFadedBgButton(
                        text = stringResource(id = R.string.text_confirm),
                        onClick = {
                            scope.launch {
                                popBackStack()
                                rewardedAdManager.tryToShowRewardedLoadingDialog(instantlyLoad = true)
                            }
                        },
                        modifier = Modifier
                            .bodyWidth()
                            .width(240.dp)
                    )
                }

                BlankSpacer(height = 16.dp)

                Text(
                    text = stringResource(id = R.string.rewarded_ad_tips_content_2),
                    color = Color.White,
                    fontSize = if (Locale.current.toLanguageTag() == "ru-RU") 15.sp else 16.sp
                )

                BlankSpacer(height = 16.dp)

                Surface(
                    shape = RoundedCornerShape(22.dp),
                ) {
//                    MRecAd(
//                        placeholder = MRecAdPlaceholder.Dialog,
//                        placeName = "reward_tips",
//                        modifier = Modifier.padding(top = 8.dp)
//                    )

                    NativeAdInDialog(
                        place = NativeAdPlace.Dialog,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }
            }
        }
    }

}

@Composable
private fun TreasureChest(
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        RewardBoxAnimation(
            modifier = Modifier
                .size(300.dp)
                .align(Alignment.TopCenter)
        )

        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 56.dp),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_badge_title),
                contentDescription = null
            )


            Text(
                text = stringResource(id = R.string.text_congratulations),
                color = Color.White,
                fontSize = 20.sp,
                modifier = Modifier.padding(bottom = 5.dp)
            )
        }

    }
}

@Composable
private fun RewardBoxAnimation(
    modifier: Modifier = Modifier
) {
    val composition by rememberLottieComposition(
        spec = LottieCompositionSpec.Asset("TreasureChest.zip"),
    )

    LottieAnimation(
        composition = composition,
        modifier = modifier,
        speed = 1f,
        iterations = Int.MAX_VALUE,
//        reverseOnRepeat = true
    )
}

@Preview
@Composable
private fun RewardedAdLoadingDialogPreview() {
    AppTheme {
        RewardedLoadingDialog(
            instantlyLoad = false,
            navUp = {},
            popBackStack = {},
            replaceToRewardedErrorDialog = {}
        )
    }
}
