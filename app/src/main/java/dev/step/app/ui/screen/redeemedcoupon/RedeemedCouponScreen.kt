package dev.step.app.ui.screen.redeemedcoupon

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.showToast
import dev.step.app.androidplatform.biz.ad.NativeAd
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.RewardedAdButton
import dev.step.app.ui.screen.withdraw.RedeemedCouponItem
import dev.step.app.ui.screen.withdraw.RedeemedPrize
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun RedeemedCouponScreen(
    coupon: RedeemedPrize.Coupon,
    navUp: () -> Unit,
    openSuccessfullyDialog: () -> Unit
) {
    val viewModel: RedeemedCouponViewModel = koinViewModel()

    LaunchedEffect(Unit) {
        viewModel.configure(coupon)
    }

    RedeemedCouponScreen(
        navUp = navUp,
        openSuccessfullyDialog = openSuccessfullyDialog,
        viewModel = viewModel
    )

}

@Composable
private fun RedeemedCouponScreen(
    navUp: () -> Unit,
    openSuccessfullyDialog: () -> Unit,
    viewModel: RedeemedCouponViewModel,
) {
    val viewState by viewModel.collectAsState()

    viewModel.collectSideEffect {
        handleRedeemedGiftCardSideEffect(
            it = it,
            onRedeemed = openSuccessfullyDialog,
            onEmailError = { showToast("invalid email") }
        )
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            RedeemedGiftCardTopBar(navUp)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(top = it.calculateTopPadding())
        ) {
            RedeemedCouponMainContent(
                coupon = viewState.coupon,
                emailTextField = viewState.emailTextField,
                messageTextField = viewState.messageTextField,
                onRedeemNow = {
                    viewModel.onRedeemNow()
                    logEventRecord("click_withdraw_redeem")
                },
                onEmailChange = { textFieldValue ->
                    viewModel.onEmailChange(textFieldValue)

                    if (!viewModel.isEmailTextFieldClicked) {
                        logEventRecord("click_withdraw_email")
                        viewModel.isEmailTextFieldClicked = true
                    }
                },
                onMessageChange = { messageTextField ->
                    viewModel.onMessageChange(messageTextField)
                    if (!viewModel.isMessageTextFieldClicked) {
                        logEventRecord("click_withdraw_messag")
                        viewModel.isMessageTextFieldClicked = true
                    }
                },
            )

            Spacer(modifier = Modifier.height(16.dp))


//            MRecAd(
//                placeholder = MRecAdPlaceholder.RedeemedScreen,
//                placeName = "withdraw_redeem"
//            )

            NativeAd(
                place = NativeAdPlace.RedeemCoupon,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
    }
}

@Composable
fun RedeemedCouponMainContent(
    coupon: RedeemedPrize.Coupon?,
    emailTextField: TextFieldValue,
    messageTextField: TextFieldValue,
    onRedeemNow: () -> Unit,
    onEmailChange: (TextFieldValue) -> Unit,
    onMessageChange: (TextFieldValue) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = stringResource(R.string.please_enter_your_gift_card_details),
                fontSize = 15.sp,
                modifier = Modifier.padding(16.dp)
            )

            val screenWidthDp = LocalConfiguration.current.screenWidthDp

            coupon?.let {
                RedeemedCouponItem(
                    currency = "$",
                    currencyPlacementIsStart = true,
                    coupon = coupon,
                    onRedeemed = {},
                    modifier = Modifier
                        .bodyWidth()
                        .width((screenWidthDp / 2).dp)
                )
            }

            BlankSpacer(height = 8.dp)


            Column(
                modifier = Modifier.padding(16.dp),
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = stringResource(R.string.redeemed_coupon_text_to),
                        fontSize = 15.sp
                    )

                    BlankSpacer(width = 8.dp)

                    TextField(
                        value = emailTextField,
                        onValueChange = onEmailChange,
                        colors = TextFieldDefaults.textFieldColors(
                            textColor = AppColor.TextColorBlack,
                            disabledTextColor = Color.Transparent,
                            backgroundColor = AppColor.PrimaryLightAlpha8,
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent,
                            disabledIndicatorColor = Color.Transparent
                        ),
                        textStyle = LocalTextStyle.current.copy(fontSize = 14.sp),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                        shape = RoundedCornerShape(5.dp),
                        placeholder = {
                            Text(
                                text = stringResource(R.string.redeemed_coupon_text_enter_your_email_here),
                                color = AppColor.TextColorGray.copy(.7f),
                                fontSize = 14.sp
                            )
                        },
                        maxLines = 1,
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                BlankSpacer(height = 16.dp)

                Text(
                    text = stringResource(R.string.redeemed_coupon_text_message),
                    fontSize = 15.sp
                )

                BlankSpacer(height = 8.dp)

                TextField(
                    value = messageTextField,
                    onValueChange = onMessageChange,
                    colors = TextFieldDefaults.textFieldColors(
                        textColor = AppColor.TextColorBlack,
                        disabledTextColor = Color.Transparent,
                        backgroundColor = AppColor.PrimaryLightAlpha8,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent,
                        disabledIndicatorColor = Color.Transparent
                    ),
                    textStyle = LocalTextStyle.current.copy(fontSize = 12.sp),
                    shape = RoundedCornerShape(5.dp),
                    placeholder = {
                        Text(
                            text = stringResource(R.string.redeemed_coupon_text_add_a_message_optional),
                            color = AppColor.TextColorGray.copy(.7f),
                            fontSize = 12.sp
                        )
                    },
                    maxLines = 4,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(82.dp)
                )

                BlankSpacer(height = 30.dp)

                RewardedAdButton(
                    text = stringResource(R.string.title_redeem_now),
                    times = null,
                    onClick = onRedeemNow,
                    modifier = Modifier.bodyWidth()
                )

                BlankSpacer(height = 16.dp)
            }
        }
    }


}


@Composable
private fun RedeemedGiftCardTopBar(
    navUp: () -> Unit,
) {
    val statusBarHeight = LocalContext.current.statusBarHeight


    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        BlankSpacer(height = statusBarHeight)

        Row(
            modifier = Modifier
                .padding(vertical = 8.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            IconButton(navUp) {
                Icon(
                    imageVector = Icons.Rounded.KeyboardArrowLeft,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = AppColor.TextColorBlack
                )
            }

            Text(
                text = stringResource(R.string.redeemed_coupon_text_redeem_your_gift_card),
                color = AppColor.TextColorBlack,
                fontSize = 20.sp,
            )
        }
    }
}

@Preview
@Composable
fun RedeemedGiftCardMainContentPreview() {
    AppTheme {
        var email by remember { mutableStateOf(TextFieldValue()) }
        var message by remember { mutableStateOf(TextFieldValue()) }

        RedeemedCouponMainContent(
            coupon = RedeemedPrize.Coupon(100000, 10),
            emailTextField = email,
            messageTextField = message,
            onRedeemNow = { /*TODO*/ },
            onEmailChange = { email = it },
            onMessageChange = { message = it }
        )
    }
}
