package dev.step.app.androidplatform.biz.ad.tradplus.nat1ve

import android.content.Context
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.nativead.NativeAdListener
import com.tradplus.ads.open.nativead.TPNative
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.AdLoadingStateEvent
import dev.step.app.androidplatform.biz.ad.AdProvider
import dev.step.app.androidplatform.biz.ad.AdShowStateEvent
import dev.step.app.androidplatform.biz.ad.NativeAdEvent
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.NativeAdState
import dev.step.app.androidplatform.biz.ad.tradplus.TradplusAdUnitIds
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class TradPlusNativeAdManager(
    private val context: Context,
    private val splashController: SplashHelper,
) {
    @Suppress("PrivatePropertyName")
    private val TAG = "TradPlusNativeAdManager"
    
    private val adUnitNameLowercase = "native"
    private val adKey = TradplusAdUnitIds.NATIVE
    
    // Map to store TPNative instances for each placement
    private val nativeAdMap = mutableMapOf<NativeAdPlace, TPNative>()
    
    // State flows for each placement
    private val adStateFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<NativeAdState>>()
    
    // Event flow for native ad events
    val eventFlow = EventFlow<NativeAdEvent>()
    
    fun loadAd(place: NativeAdPlace, context: Context) {
        debugLog(tag = TAG) { "loadAd for place: ${place.name}" }
        
        // Initialize or get existing TPNative instance
        val tpNative = nativeAdMap.getOrPut(place) {
            TPNative(context, adKey).apply {
                setAdListener(setupNativeAdListener(place))
            }
        }
        
        // Update state to loading
        updateAdState(place) { currentState ->
            currentState.copy(
                isLoading = true,
                isLoaded = false,
                error = null,
                provider = AdProvider.TRADPLUS
            )
        }
        
        // Load the ad
        tpNative.loadAd()
        
        // Log event
        logEventRecord("ad_${adUnitNameLowercase}_load")
    }
    
    fun destroyAd(place: NativeAdPlace) {
        debugLog(tag = TAG) { "destroyAd for place: ${place.name}" }
        
        nativeAdMap[place]?.let { tpNative ->
            tpNative.onDestroy()
            nativeAdMap.remove(place)
        }
        
        // Reset state
        updateAdState(place) { NativeAdState.Empty }
    }
    
    fun isAdAvailable(place: NativeAdPlace): Boolean {
        val tpNative = nativeAdMap[place]
        return tpNative?.isReady() == true
    }
    
    fun getAdState(place: NativeAdPlace): StateFlow<NativeAdState> {
        return adStateFlowMap.getOrPut(place) {
            MutableStateFlow(NativeAdState.Empty)
        }
    }
    
    fun getNativeAd(place: NativeAdPlace): TPNative? {
        return nativeAdMap[place]
    }
    
    private fun updateAdState(place: NativeAdPlace, update: (NativeAdState) -> NativeAdState) {
        val stateFlow = adStateFlowMap.getOrPut(place) {
            MutableStateFlow(NativeAdState.Empty)
        }
        stateFlow.update(update)
    }
    
    private fun setupNativeAdListener(place: NativeAdPlace): NativeAdListener {
        return object : NativeAdListener() {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?, tpBaseAd: com.tradplus.ads.base.bean.TPBaseAd?) {
                debugLog(tag = TAG) { "place:${place.name} onAdLoaded" }
                
                updateAdState(place) { currentState ->
                    currentState.copy(
                        isLoading = false,
                        isLoaded = true,
                        error = null,
                        provider = AdProvider.TRADPLUS
                    )
                }
                
                // Send events
                GlobalScope.launch(Dispatchers.Main) {
                    eventFlow.send(
                        NativeAdEvent.LoadingStateEvent(
                            place,
                            AdLoadingStateEvent.Loaded
                        )
                    )
                }
                
                logEventRecord("ad_${adUnitNameLowercase}_load_success")
            }
            
            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "place:${place.name} onAdClicked" }
                
                splashController.doSkipSplash(true)
                
                // Send click event
                GlobalScope.launch(Dispatchers.Main) {
                    eventFlow.send(NativeAdEvent.ClickEvent(place))
                }
                
                logEventRecord("ad_${adUnitNameLowercase}_click")
            }
            
            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "place:${place.name} onAdImpression" }
                
                // Send impression event
                GlobalScope.launch(Dispatchers.Main) {
                    eventFlow.send(NativeAdEvent.ImpressionEvent(place))
                }
                
                logEventRecord("ad_${adUnitNameLowercase}_impress")
            }
            
            override fun onAdLoadFailed(tpAdError: TPAdError?) {
                debugLog(tag = TAG) { "place:${place.name} onAdLoadFailed: ${tpAdError?.errorMsg}" }
                
                val errorMsg = tpAdError?.errorMsg ?: "Unknown error"
                
                updateAdState(place) { currentState ->
                    currentState.copy(
                        isLoading = false,
                        isLoaded = false,
                        error = errorMsg,
                        provider = AdProvider.TRADPLUS
                    )
                }
                
                // Send error event
                GlobalScope.launch(Dispatchers.Main) {
                    eventFlow.send(
                        NativeAdEvent.LoadingStateEvent(
                            place,
                            AdLoadingStateEvent.FailedToLoad
                        )
                    )
                }
            }
            
            override fun onAdShowFailed(tpAdError: TPAdError?, tpAdInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "place:${place.name} onAdShowFailed: ${tpAdError?.errorMsg}" }
                
                // Send show error event
                GlobalScope.launch(Dispatchers.Main) {
                    eventFlow.send(
                        NativeAdEvent.ShowStateEvent(
                            place,
                            AdShowStateEvent.FailedToShow
                        )
                    )
                }
            }
            
            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "place:${place.name} onAdClosed" }
                
                // Send close event
                GlobalScope.launch(Dispatchers.Main) {
                    eventFlow.send(
                        NativeAdEvent.ShowStateEvent(
                            place,
                            AdShowStateEvent.Finish
                        )
                    )
                }
            }
        }
    }
    
    fun onDestroy() {
        debugLog(tag = TAG) { "onDestroy - cleaning up all ads" }
        
        nativeAdMap.values.forEach { tpNative ->
            tpNative.onDestroy()
        }
        
        nativeAdMap.clear()
        adStateFlowMap.clear()
    }
}