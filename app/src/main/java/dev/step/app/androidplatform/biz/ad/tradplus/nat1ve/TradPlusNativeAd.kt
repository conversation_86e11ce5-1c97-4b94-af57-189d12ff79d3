package dev.step.app.androidplatform.biz.ad.tradplus.nat1ve

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewParent
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import com.tradplus.ads.base.adapter.nativead.TPNativeAdView
import com.tradplus.ads.base.common.TPImageLoader
import com.tradplus.ads.open.nativead.TPNative
import com.tradplus.ads.open.nativead.TPNativeAdRender
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.defaultNativeAdBorderStroke
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.emptyNativeAdBorderStroke
import dev.step.app.androidplatform.biz.ad.isDialogPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.ui.theme.bodyWidth
import org.koin.compose.koinInject

@Composable
fun TradPlusNativeAdInDialog(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
    borderStroke: BorderStroke? = emptyNativeAdBorderStroke
) {
    TradPlusNativeAd(
        place = place,
        modifier = modifier,
        borderStroke = borderStroke
    )
}

@Composable
fun TradPlusNativeAd(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
    borderStroke: BorderStroke? = defaultNativeAdBorderStroke
) {
    val localContext = LocalContext.current
    val nativeAdManager: TradPlusNativeAdManager = koinInject()
    
    val adState by nativeAdManager.getAdState(place).collectAsState()
    
    debugLog(tag = "TRADPLUS_NATIVE_AD") { "adState: $adState" }
    
    var adContainer by remember { mutableStateOf<FrameLayout?>(null) }
    val nativeAdContentBinding by remember { mutableStateOf(localContext.nativeAdContentBinding()) }
    
    // Lifecycle management
    if (!place.isDialogPlace()) {
        OnLifecycleEvent { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    debugLog(tag = "TRADPLUS_NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }
                    nativeAdManager.destroyAd(place)
                }
                else -> {}
            }
        }
    } else {
        DisposableEffect(Unit) {
            onDispose {
                debugLog(tag = "TRADPLUS_NATIVE_AD") { "DisposableEffect onDispose call destroy() place: ${place.name}" }
                nativeAdManager.destroyAd(place)
            }
        }
    }
    
    // Handle ad loading and display
    LaunchedEffect(adState.isLoaded) {
        if (adState.isLoaded) {
            val tpNative = nativeAdManager.getNativeAd(place)
            tpNative?.let {
                debugLog(tag = "TRADPLUS_NATIVE_AD") { "Configuring TradPlus native ad" }
                nativeAdContentBinding.configureTradPlus(it)
                adContainer?.removeAllViews()
                adContainer?.addView(nativeAdContentBinding.root)
            }
        }
    }
    
    // Load ad if not loaded yet
    LaunchedEffect(Unit) {
        logEventRecord("ad_native_show")
        
        if (!adState.isLoaded && !adState.isLoading) {
            debugLog(tag = "TRADPLUS_NATIVE_AD") { "Loading TradPlus native ad for place: ${place.name}" }
            nativeAdManager.loadAd(place, localContext)
        }
    }
    
    // Adaptive height based on ad state and place
    val adaptiveModifier = if (place == NativeAdPlace.Dialog || adState.isLoaded)
        modifier.height(262.dp)
    else
        modifier.height(0.dp)
    
    AndroidView(
        factory = { context ->
            FrameLayout(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            }.apply {
                adContainer = this
            }
        },
        modifier = adaptiveModifier
            .border(borderStroke ?: emptyNativeAdBorderStroke)
            .animateContentSize()
            .background(Color.Transparent)
            .bodyWidth(),
    )
}

private fun Context.nativeAdContentBinding(): LayoutNativeAdForTradplusBinding {
    val layoutInflater = this.findActivity().layoutInflater
    return LayoutNativeAdForTradplusBinding.inflate(layoutInflater)
}

private fun LayoutNativeAdForTradplusBinding.configureTradPlus(tpNative: TPNative) {
    val nativeAdView = this.nativeAd
    
    // Configure the native ad view for TradPlus
    // Note: TradPlus uses a different approach than AdMob
    // We need to set up the views and then call renderAdView

    tpNative.nativeAd.nativeAdView
    
    // Set up view mappings
    nativeAdView.mediaView = this.adMedia
    nativeAdView.headlineView = this.adHeadline
    nativeAdView.bodyView = this.adBody
    nativeAdView.callToActionView = this.adCallToAction
    nativeAdView.iconView = this.adAppIcon
    nativeAdView.advertiserView = this.adAdvertiser
    
    // Let TradPlus handle the rendering
    try {
        // TradPlus renders automatically when the ad is loaded
        // We just need to set up the view hierarchy correctly
        debugLog(tag = "TRADPLUS_NATIVE_AD") { "TradPlus native ad view configured" }
    } catch (e: Exception) {
        debugLog(tag = "TRADPLUS_NATIVE_AD") { "Error configuring TradPlus native ad: ${e.message}" }
    }
}