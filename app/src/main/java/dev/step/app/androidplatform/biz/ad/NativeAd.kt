package dev.step.app.androidplatform.biz.ad

import androidx.compose.foundation.BorderStroke
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAd
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAdInDialog
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.defaultNativeAdBorderStroke
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.emptyNativeAdBorderStroke
import dev.step.app.androidplatform.biz.ad.tradplus.nat1ve.TradPlusNativeAd
import dev.step.app.androidplatform.biz.ad.tradplus.nat1ve.TradPlusNativeAdInDialog
import org.koin.compose.koinInject

@Composable
fun NativeAd(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
    borderStroke: BorderStroke? = defaultNativeAdBorderStroke
) {
    val nativeAdFacade: NativeAdFacade = koinInject()
    
    when (nativeAdFacade.getCurrentProvider()) {
        AdProvider.ADMOB -> {
            AdmobNativeAd(
                place = place,
                modifier = modifier,
                borderStroke = borderStroke
            )
        }
        AdProvider.TRADPLUS -> {
            TradPlusNativeAd(
                place = place,
                modifier = modifier,
                borderStroke = borderStroke
            )
        }
    }
}

@Composable
fun NativeAdInDialog(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
    borderStroke: BorderStroke? = emptyNativeAdBorderStroke
) {
    val nativeAdFacade: NativeAdFacade = koinInject()
    
    when (nativeAdFacade.getCurrentProvider()) {
        AdProvider.ADMOB -> {
            AdmobNativeAdInDialog(
                place = place,
                modifier = modifier,
                borderStroke = borderStroke
            )
        }
        AdProvider.TRADPLUS -> {
            TradPlusNativeAdInDialog(
                place = place,
                modifier = modifier,
                borderStroke = borderStroke
            )
        }
    }
}