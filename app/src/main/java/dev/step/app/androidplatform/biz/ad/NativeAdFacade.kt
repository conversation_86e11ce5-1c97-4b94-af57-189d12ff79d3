package dev.step.app.androidplatform.biz.ad

import android.content.Context
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.nat1ve.TradPlusNativeAdManager
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class NativeAdFacade(
    private val admobManager: AdmobNativeAdManager,
    private val tradplusManager: TradPlusNativeAdManager,
    private val remoteConfig: FirebaseRemoteConfigHelper
) {
    companion object {
        private val DEFAULT_PROVIDER = AdProvider.TRADPLUS
    }

    // Cache to avoid frequent remote config reads
    private var cachedProvider: AdProvider? = null

    fun getCurrentProvider(): AdProvider {
        return cachedProvider ?: remoteConfig.getAdProvider().also { cachedProvider = it }
    }

    fun refreshProvider() {
        cachedProvider = null
    }

    fun loadAd(place: NativeAdPlace, context: Context) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.buildAd(place)
            AdProvider.TRADPLUS -> tradplusManager.loadAd(place, context)
        }
    }

    fun destroyAd(place: NativeAdPlace) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.destroy(place)
            AdProvider.TRADPLUS -> tradplusManager.destroyAd(place)
        }
    }

    fun getAdState(place: NativeAdPlace): StateFlow<NativeAdState> {
        return when (getCurrentProvider()) {
            AdProvider.ADMOB -> {
                admobManager.getAdFlow(place).map { nativeAd ->
                    NativeAdState(
                        isLoading = false,
                        isLoaded = nativeAd != null,
                        error = null,
                        provider = AdProvider.ADMOB
                    )
                }.stateIn(
                    scope = GlobalScope,
                    started = kotlinx.coroutines.flow.SharingStarted.Eagerly,
                    initialValue = NativeAdState.Empty
                )
            }
            AdProvider.TRADPLUS -> tradplusManager.getAdState(place)
        }
    }

    fun isAdAvailable(place: NativeAdPlace): Boolean {
        return when (getCurrentProvider()) {
            AdProvider.ADMOB -> {
                admobManager.getAdFlow(place).value != null
            }
            AdProvider.TRADPLUS -> tradplusManager.isAdAvailable(place)
        }
    }

    // Event flows
    val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AdShowStateEvent>()
    val nativeAdEventFlow = EventFlow<NativeAdEvent>()

    init {
        setupEventFlowAdapters()
    }

    private fun setupEventFlowAdapters() {
        // TradPlus events will be handled by TradPlusNativeAdManager
        // For now, we'll focus on the basic implementation
        GlobalScope.launch(Dispatchers.Main) {
            tradplusManager.eventFlow.collect { event ->
                if (getCurrentProvider() == AdProvider.TRADPLUS) {
                    nativeAdEventFlow.send(event)
                }
            }
        }
    }

    fun onDestroy() {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.destroyAll()
            AdProvider.TRADPLUS -> tradplusManager.onDestroy()
        }
    }
}