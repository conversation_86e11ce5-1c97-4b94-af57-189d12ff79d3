<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- AD label in top left corner -->
    <LinearLayout
        android:id="@+id/ll_ad_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="4dp"
            app:cardElevation="0dp"
            app:cardUseCompatPadding="false">

            <TextView
                android:id="@+id/tvAd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#F75927"
                android:paddingHorizontal="4dp"
                android:paddingVertical="1dp"
                android:text=" AD "
                android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:textStyle="bold" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/ad_advertiser"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textColor="#666666"
            android:textSize="10sp"
            tools:text="Advertiser" />
    </LinearLayout>

    <!-- Top header section with icon, title and CTA button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp"
        android:paddingTop="4dp">

        <!-- App icon -->
        <androidx.cardview.widget.CardView
            android:id="@+id/mcv_icon_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="6dp"
            app:cardElevation="0dp"
            app:cardUseCompatPadding="false">

            <ImageView
                android:id="@+id/ad_app_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                tools:src="@drawable/ic_app_launcher" />

        </androidx.cardview.widget.CardView>

        <!-- Title and description section -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAppearance="@style/TextAppearance.AppCompat.Title"
                android:textColor="#000000"
                android:textSize="13sp"
                android:textStyle="bold"
                tools:text="Test Ad" />

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textColor="#666666"
                android:textSize="12sp"
                tools:text="This is a Test Ad" />

        </LinearLayout>

        <!-- CTA Button -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_ad_call_to_action_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:cardBackgroundColor="#F75927"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">

            <Button
                android:id="@+id/ad_call_to_action"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="@android:color/transparent"
                android:elevation="0dp"
                android:minWidth="80dp"
                android:paddingHorizontal="20dp"
                android:textAllCaps="true"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="CLICK" />
        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- Main media content -->
    <ImageView
        android:id="@+id/ad_media"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="8dp"
        android:layout_marginBottom="8dp"
        android:layout_weight="1"
        android:minHeight="120dp"
        android:scaleType="centerCrop"
        tools:background="@color/cardview_light_background" />

</LinearLayout>
